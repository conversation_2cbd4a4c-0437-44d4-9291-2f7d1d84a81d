@import "tailwindcss" important;
@import "tailwindcss/preflight";


@font-face {
    font-family: 'Sofia Pro';
    src: url('../Fonts/SofiaPro Regular Az.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sofia Pro';
    src: url('../Fonts/SofiaPro Medium Az.otf') format('opentype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sofia Pro';
    src: url('../Fonts/SofiaPro Bold Az.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}



html {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif;
}

body {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

/* DevExpress overrides */

/* AntDesign Steps*/

.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {
    background-color: #5eead4 !important;
}

.voyage-steps .ant-steps-item-finish .ant-steps-item-icon {
    background-color: #0f766e !important;
    border-color: #0f766e !important;
}

    .voyage-steps .ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
        color: white !important;
    }

.voyage-steps .ant-steps-item-process .ant-steps-item-icon {
    background-color: #0f766e !important;
    border-color: #0f766e !important;
}

    .voyage-steps .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon {
        color: white !important;
    }

.voyage-steps .ant-steps-item-finish .ant-steps-item-title {
    color: #0f766e !important;
}

.voyage-steps .ant-steps-item-process .ant-steps-item-title {
    color: #0f766e !important;
}

.voyage-steps .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after {
    background-color: #0f766e !important;
}

/* AntDesign Table Pagination overrides */

/* Pagination container styling */
.ant-pagination {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

/* Pagination page number buttons */
.ant-pagination .ant-pagination-item {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
}

.ant-pagination .ant-pagination-item a {
    color: #6b7280 !important; /* gray-500 */
    font-weight: 500 !important;
}

.ant-pagination .ant-pagination-item:hover {
    border-color: #0f766e !important; /* teal-700 */
    background-color: #f0fdfa !important; /* teal-50 */
}

.ant-pagination .ant-pagination-item:hover a {
    color: white !important; /* teal-700 */
}

/* Active page number */
.ant-pagination .ant-pagination-item-active {
    border-color: #0f766e !important; /* teal-700 */
    background-color: #0f766e !important; /* teal-700 */
}

.ant-pagination .ant-pagination-item-active a {
    color: #ffffff !important;
    font-weight: 600 !important;
}

.ant-pagination .ant-pagination-item-active:hover {
    border-color: #0d9488 !important; /* teal-600 */
    background-color: #0d9488 !important; /* teal-600 */
}

/* Previous/Next navigation buttons */
.ant-pagination .ant-pagination-prev,
.ant-pagination .ant-pagination-next {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
}

.ant-pagination .ant-pagination-prev:hover,
.ant-pagination .ant-pagination-next:hover {
    border-color: #0f766e !important; /* teal-700 */
    background-color: #f0fdfa !important; /* teal-50 */
}

.ant-pagination .ant-pagination-prev .ant-pagination-item-link,
.ant-pagination .ant-pagination-next .ant-pagination-item-link {
    color: #6b7280 !important; /* gray-500 */
}

.ant-pagination .ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination .ant-pagination-next:hover .ant-pagination-item-link {
    color: #0f766e !important; /* teal-700 */
}

/* Disabled navigation buttons */
.ant-pagination .ant-pagination-disabled {
    border-color: #e5e7eb !important; /* gray-200 */
    background-color: #f9fafb !important; /* gray-50 */
}

.ant-pagination .ant-pagination-disabled .ant-pagination-item-link {
    color: #d1d5db !important; /* gray-300 */
}

/* Page size selector */
.ant-pagination .ant-pagination-options .ant-select-selector {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
}

.ant-pagination .ant-pagination-options .ant-select-selector:hover {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-pagination .ant-pagination-options .ant-select-focused .ant-select-selector {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

/* Jump to page input */
.ant-pagination .ant-pagination-simple .ant-pagination-simple-pager input {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
}

.ant-pagination .ant-pagination-simple .ant-pagination-simple-pager input:hover {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-pagination .ant-pagination-simple .ant-pagination-simple-pager input:focus {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

/* AntDesign Table Sorting and Filtering overrides */

/* Table column headers */
.ant-table-thead > tr > th {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    background-color: #f9fafb !important; /* gray-50 */
    border-bottom: 1px solid #e5e7eb !important; /* gray-200 */
}

.ant-table-thead > tr > th:hover {
    background-color: #f0fdfa !important; /* teal-50 */
}

/* Sortable column headers */
.ant-table-thead > tr > th.ant-table-column-has-sorters {
    cursor: pointer !important;
    transition: background-color 0.2s ease !important;
}

.ant-table-thead > tr > th.ant-table-column-has-sorters:hover {
    background-color: #f0fdfa !important; /* teal-50 */
}

.ant-table-thead > tr > th.ant-table-column-has-sorters:hover .ant-table-column-title {
    color: #0f766e !important; /* teal-700 */
}

/* Sort indicators (arrows) */
.ant-table-column-sorter {
    color: #d1d5db !important; /* gray-300 */
    transition: color 0.2s ease !important;
}

.ant-table-column-sorter:hover {
    color: #0f766e !important; /* teal-700 */
}

.ant-table-column-sorter-up.active,
.ant-table-column-sorter-down.active {
    color: #0f766e !important; /* teal-700 */
}

.ant-table-column-sorter-inner {
    color: inherit !important;
}

/* Active sorted column */
.ant-table-thead > tr > th.ant-table-column-sort {
    background-color: #f0fdfa !important; /* teal-50 */
}

.ant-table-thead > tr > th.ant-table-column-sort .ant-table-column-title {
    color: #0f766e !important; /* teal-700 */
    font-weight: 600 !important;
}

/* Filter trigger icon */
.ant-table-filter-trigger {
    color: #6b7280 !important; /* gray-500 */
    transition: color 0.2s ease !important;
}

.ant-table-filter-trigger:hover {
    color: #0f766e !important; /* teal-700 */
    background-color: #f0fdfa !important; /* teal-50 */
}

.ant-table-filter-trigger.active {
    color: #0f766e !important; /* teal-700 */
    background-color: #f0fdfa !important; /* teal-50 */
}

/* Filter dropdown container */
.ant-table-filter-dropdown {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important; /* gray-200 */
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
    padding: 1rem !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

/* Filter dropdown search input */
.ant-table-filter-dropdown .ant-input {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-radius: 0.375rem !important;
}

.ant-table-filter-dropdown .ant-input:hover {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-input:focus,
.ant-table-filter-dropdown .ant-input-focused {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

/* Filter dropdown checkbox list */
.ant-table-filter-dropdown .ant-checkbox-wrapper {
    color: #374151 !important; /* gray-700 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    padding: 0.25rem 0 !important;
}

.ant-table-filter-dropdown .ant-checkbox-wrapper:hover {
    color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-checkbox-wrapper:hover .ant-checkbox-inner {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #0f766e !important; /* teal-700 */
    border-color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-checkbox-checked:after {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-checkbox-indeterminate .ant-checkbox-inner {
    background-color: #0f766e !important; /* teal-700 */
    border-color: #0f766e !important; /* teal-700 */
}

/* Filter dropdown buttons */
.ant-table-filter-dropdown .ant-table-filter-dropdown-btns {
    border-top: 1px solid #e5e7eb !important; /* gray-200 */
    padding-top: 0.75rem !important;
    margin-top: 0.75rem !important;
    display: flex !important;
    justify-content: space-between !important;
    gap: 0.5rem !important;
}

.ant-table-filter-dropdown .ant-btn {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

/* Filter dropdown OK button */
.ant-table-filter-dropdown .ant-btn-primary {
    background-color: #0f766e !important; /* teal-700 */
    border-color: #0f766e !important; /* teal-700 */
    color: #ffffff !important;
}

.ant-table-filter-dropdown .ant-btn-primary:hover {
    background-color: #0d9488 !important; /* teal-600 */
    border-color: #0d9488 !important; /* teal-600 */
}

.ant-table-filter-dropdown .ant-btn-primary:focus {
    background-color: #0d9488 !important; /* teal-600 */
    border-color: #0d9488 !important; /* teal-600 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

/* Filter dropdown Reset button */
.ant-table-filter-dropdown .ant-btn-link {
    color: #6b7280 !important; /* gray-500 */
    border: none !important;
    background: transparent !important;
}

.ant-table-filter-dropdown .ant-btn-link:hover {
    color: #0f766e !important; /* teal-700 */
    background-color: #f0fdfa !important; /* teal-50 */
}

/* Filter dropdown menu items */
.ant-table-filter-dropdown .ant-dropdown-menu {
    background-color: #ffffff !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.ant-table-filter-dropdown .ant-dropdown-menu-item {
    color: #374151 !important; /* gray-700 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-table-filter-dropdown .ant-dropdown-menu-item:hover {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-dropdown-menu-item-selected {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
}

/* Table body row interactions */
.ant-table-tbody > tr:hover > td {
    background-color: #f0fdfa !important; /* teal-50 */
}

.ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: #f0fdfa !important; /* teal-50 */
    border-color: #0f766e !important; /* teal-700 */
}

.ant-table-tbody > tr.ant-table-row-selected:hover > td {
    background-color: #e6fffa !important; /* teal-25 - lighter than teal-50 */
}

/* Filter active indicator */
.ant-table-thead > tr > th.ant-table-column-has-filters.ant-table-filter-column {
    background-color: #f0fdfa !important; /* teal-50 */
}

.ant-table-thead > tr > th.ant-table-column-has-filters.ant-table-filter-column .ant-table-column-title {
    color: #0f766e !important; /* teal-700 */
    font-weight: 600 !important;
}

/* Custom filter dropdown positioning */
.ant-table-filter-dropdown-container {
    z-index: 1050 !important;
}

/* Ensure consistent font family across all table elements */
.ant-table {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-table-tbody > tr > td {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-bottom: 1px solid #f3f4f6 !important; /* gray-100 */
}

/* Loading state styling */
.ant-table-placeholder .ant-empty-description {
    color: #6b7280 !important; /* gray-500 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-spin-dot-item {
    background-color: #0f766e !important; /* teal-700 */
}

/* Table expand/collapse functionality styling - minimal changes for expanded row only */

.ant-table-expanded-row > td {
    padding: 0 !important;
    background-color: #f3f4f6 !important; /* slate-50 - subtle background */
    border-bottom: 1px solid #e2e8f0 !important; /* slate-200 */
}

.ant-table-expanded-row .ant-table-cell {
    border-bottom: none !important;
}

/* Enhanced styling for expanded content card */
.ant-table-expanded-row .ant-table-cell > div {
    margin: 0 !important;
}

/* Enhanced badge styling in expanded view */
.ant-table-expanded-row .flex.flex-wrap span {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.ant-table-expanded-row .flex.flex-wrap span:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced empty state styling */
.ant-table-expanded-row .text-center {
    background-color: #ffffff !important;
    border-radius: 0.5rem !important;
    border: 1px dashed #d1d5db !important; /* gray-300 */
    margin: 0.5rem 0 !important;
}



/* Filter type selector dropdown within filter dropdown */
.ant-table-filter-dropdown .ant-select {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-table-filter-dropdown .ant-select .ant-select-selector {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
    border-radius: 0.375rem !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-table-filter-dropdown .ant-select .ant-select-selector:hover {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-select.ant-select-focused .ant-select-selector,
.ant-table-filter-dropdown .ant-select.ant-select-open .ant-select-selector {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

.ant-table-filter-dropdown .ant-select .ant-select-selection-item {
    color: #374151 !important; /* gray-700 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    font-weight: 500 !important;
}

.ant-table-filter-dropdown .ant-select .ant-select-selection-placeholder {
    color: #9ca3af !important; /* gray-400 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-table-filter-dropdown .ant-select .ant-select-arrow {
    color: #6b7280 !important; /* gray-500 */
}

.ant-table-filter-dropdown .ant-select:hover .ant-select-arrow {
    color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-select.ant-select-focused .ant-select-arrow,
.ant-table-filter-dropdown .ant-select.ant-select-open .ant-select-arrow {
    color: #0f766e !important; /* teal-700 */
}

/* Filter type selector dropdown menu */
.ant-select-dropdown.ant-table-filter-dropdown-menu {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important; /* gray-200 */
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item {
    color: #374151 !important; /* gray-700 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 0.25rem !important;
    margin: 0.125rem !important;
}

/* AntDesign Form Elements Teal Theme Overrides */

/* Checkbox styling */
.ant-checkbox-wrapper {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-checkbox-inner {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
    border-radius: 0.25rem !important;
}

.ant-checkbox:hover .ant-checkbox-inner {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-checkbox-checked .ant-checkbox-inner {
    background-color: #0f766e !important; /* teal-700 */
    border-color: #0f766e !important; /* teal-700 */
}

.ant-checkbox-checked:after {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-checkbox-indeterminate .ant-checkbox-inner {
    background-color: #0f766e !important; /* teal-700 */
    border-color: #0f766e !important; /* teal-700 */
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-checkbox-input:focus + .ant-checkbox-inner {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

/* TimePicker styling */
.ant-picker {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-radius: 0.375rem !important;
}

.ant-picker:hover {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-picker-focused {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

.ant-picker-input > input {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    color: #374151 !important; /* gray-700 */
}

.ant-picker-input > input::placeholder {
    color: #9ca3af !important; /* gray-400 */
}

/* TimePicker dropdown panel */
.ant-picker-dropdown {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important; /* gray-200 */
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    background-color: #0f766e !important; /* teal-700 */
    color: #ffffff !important;
}

.ant-picker-time-panel-column > li.ant-picker-time-panel-cell:hover .ant-picker-time-panel-cell-inner {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
}

/* TextArea styling */
.ant-input {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-radius: 0.375rem !important;
    color: #374151 !important; /* gray-700 */
}

.ant-input:hover {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-input:focus,
.ant-input-focused {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

.ant-input::placeholder {
    color: #9ca3af !important; /* gray-400 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

/* InputNumber styling */
.ant-input-number {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-radius: 0.375rem !important;
}

.ant-input-number:hover {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-input-number-focused {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

.ant-input-number-input {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    color: #374151 !important; /* gray-700 */
}

.ant-input-number-handler-wrap {
    border-left-color: #d1d5db !important; /* gray-300 */
}

.ant-input-number-handler {
    border-color: #d1d5db !important; /* gray-300 */
    color: #6b7280 !important; /* gray-500 */
}

.ant-input-number-handler:hover {
    color: #0f766e !important; /* teal-700 */
    background-color: #f0fdfa !important; /* teal-50 */
}

/* Select dropdown styling */
.ant-select {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select .ant-select-selector {
    border-color: #d1d5db !important; /* gray-300 */
    background-color: #ffffff !important;
    border-radius: 0.375rem !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select .ant-select-selector:hover {
    border-color: #0f766e !important; /* teal-700 */
}

.ant-select.ant-select-focused .ant-select-selector,
.ant-select.ant-select-open .ant-select-selector {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

.ant-select .ant-select-selection-item {
    color: #374151 !important; /* gray-700 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    font-weight: 500 !important;
}

.ant-select .ant-select-selection-placeholder {
    color: #9ca3af !important; /* gray-400 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select .ant-select-arrow {
    color: #6b7280 !important; /* gray-500 */
}

.ant-select:hover .ant-select-arrow {
    color: #0f766e !important; /* teal-700 */
}

.ant-select.ant-select-focused .ant-select-arrow,
.ant-select.ant-select-open .ant-select-arrow {
    color: #0f766e !important; /* teal-700 */
}

/* Select dropdown menu */
.ant-select-dropdown {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important; /* gray-200 */
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select-item {
    color: #374151 !important; /* gray-700 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 0.25rem !important;
    margin: 0.125rem !important;
}

.ant-select-item:hover {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: #0f766e !important; /* teal-700 */
    color: #ffffff !important;
    font-weight: 600 !important;
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled):hover {
    background-color: #0d9488 !important; /* teal-600 */
    color: #ffffff !important;
}

.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item:hover {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
}



.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item-option-selected {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
    font-weight: 600 !important;
}

.ant-select-item-option-selected {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
    font-weight: 600 !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

/* Ensure selected item styling persists on hover */
.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item-option-selected:not(.ant-select-item-option-disabled):hover {
    background-color: #e6fffa !important; /* slightly lighter teal */
    color: #0f766e !important; /* teal-700 */
    font-weight: 600 !important;
}

.ant-select-dropdown.ant-table-filter-dropdown-menu .ant-select-item-option-active {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
}

/* Additional filter type selector states */
.ant-table-filter-dropdown .ant-select.ant-select-disabled .ant-select-selector {
    background-color: #f9fafb !important; /* gray-50 */
    border-color: #e5e7eb !important; /* gray-200 */
    color: #9ca3af !important; /* gray-400 */
}

.ant-table-filter-dropdown .ant-select.ant-select-disabled .ant-select-arrow {
    color: #d1d5db !important; /* gray-300 */
}

/* Filter type selector clear button */
.ant-table-filter-dropdown .ant-select .ant-select-clear {
    color: #6b7280 !important; /* gray-500 */
    background-color: #ffffff !important;
    border-radius: 50% !important;
}

.ant-table-filter-dropdown .ant-select .ant-select-clear:hover {
    color: #0f766e !important; /* teal-700 */
    background-color: #f0fdfa !important; /* teal-50 */
}

/* Ensure dropdown positioning and z-index */
.ant-select-dropdown.ant-table-filter-dropdown-menu {
    z-index: 1060 !important;
}

/* Filter type selector loading state */
.ant-table-filter-dropdown .ant-select .ant-select-selection-search-input {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-table-filter-dropdown .ant-select-loading .ant-select-arrow .anticon {
    color: #0f766e !important; /* teal-700 */
}

/* Multiple selection tags (if applicable) */
.ant-table-filter-dropdown .ant-select-multiple .ant-select-selection-item {
    background-color: #f0fdfa !important; /* teal-50 */
    border: 1px solid #14b8a6 !important; /* teal-500 */
    color: #0f766e !important; /* teal-700 */
    border-radius: 0.25rem !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-table-filter-dropdown .ant-select-multiple .ant-select-selection-item-remove {
    color: #0f766e !important; /* teal-700 */
}

.ant-table-filter-dropdown .ant-select-multiple .ant-select-selection-item-remove:hover {
    color: #dc2626 !important; /* red-600 */
    background-color: #fef2f2 !important; /* red-50 */
}

/* Table responsiveness and mobile optimization */
.ant-table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
}

.ant-table {
    min-width: 800px !important; /* Minimum width to maintain table structure */
}

/* Ensure table scrolls smoothly on mobile */
@media (max-width: 768px) {
    .ant-table-wrapper {
        margin: 0 -1rem !important; /* Extend to screen edges on mobile */
        padding: 0 1rem !important;
    }

    .ant-table-thead > tr > th {
        white-space: nowrap !important;
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    .ant-table-tbody > tr > td {
        white-space: nowrap !important;
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    /* Adjust filter dropdown positioning on mobile */
    .ant-table-filter-dropdown {
        max-width: 280px !important;
        max-height: 400px !important;
        overflow-y: auto !important;
    }
}

/* Ensure fixed columns work properly with horizontal scroll */
.ant-table-fixed-left,
.ant-table-fixed-right {
    z-index: 2 !important;
}

.ant-table-fixed-left .ant-table-thead > tr > th,
.ant-table-fixed-right .ant-table-thead > tr > th {
    background-color: #f9fafb !important; /* gray-50 */
}

.ant-table-fixed-left .ant-table-tbody > tr > td,
.ant-table-fixed-right .ant-table-tbody > tr > td {
    background-color: #ffffff !important;
}

/* Horizontal scroll indicator styling */
.ant-table-wrapper::-webkit-scrollbar {
    height: 8px !important;
}

.ant-table-wrapper::-webkit-scrollbar-track {
    background-color: #f3f4f6 !important; /* gray-100 */
    border-radius: 4px !important;
}

.ant-table-wrapper::-webkit-scrollbar-thumb {
    background-color: #d1d5db !important; /* gray-300 */
    border-radius: 4px !important;
}

.ant-table-wrapper::-webkit-scrollbar-thumb:hover {
    background-color: #0f766e !important; /* teal-700 */
}

/* AntDesign Form Components Styling */

/* Keep Sofia Pro font family for components without Tailwind classes */
.ant-input {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-input::placeholder {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

/* Input Number components - keep font family and handler styling */
.ant-input-number {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-input-number .ant-input-number-input {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    color: #374151 !important; /* gray-700 */
}

.ant-input-number .ant-input-number-handler {
    border-color: #d1d5db !important; /* gray-300 */
    color: #6b7280 !important; /* gray-500 */
    transition: all 0.2s ease !important;
}

.ant-input-number .ant-input-number-handler:hover {
    color: #0f766e !important; /* teal-700 */
    background-color: #f0fdfa !important; /* teal-50 */
    border-color: #0f766e !important; /* teal-700 */
}

/* Select components - keep font family and arrow styling */
.ant-select {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select .ant-select-selector {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select .ant-select-selection-item {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    font-weight: 500 !important;
}

.ant-select .ant-select-selection-placeholder {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select .ant-select-arrow {
    color: #6b7280 !important; /* gray-500 */
}

.ant-select:hover .ant-select-arrow,
.ant-select.ant-select-focused .ant-select-arrow,
.ant-select.ant-select-open .ant-select-arrow {
    color: #0f766e !important; /* teal-700 */
}

/* Select dropdown menu */
.ant-select-dropdown {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important; /* gray-200 */
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-select-dropdown .ant-select-item {
    color: #374151 !important; /* gray-700 */
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 0.25rem !important;
    margin: 0.125rem !important;
}

.ant-select-dropdown .ant-select-item:hover {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
}

.ant-select-dropdown .ant-select-item-option-selected {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
    font-weight: 600 !important;
}

.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
    font-weight: 600 !important;
}

.ant-select-dropdown .ant-select-item-option-active {
    background-color: #f0fdfa !important; /* teal-50 */
    color: #0f766e !important; /* teal-700 */
}

/* Form validation styling */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-number,
.ant-form-item-has-error .ant-select .ant-select-selector {
    border-color: #dc2626 !important; /* red-600 */
}

.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-input-focused,
.ant-form-item-has-error .ant-input-number:focus-within,
.ant-form-item-has-error .ant-input-number-focused,
.ant-form-item-has-error .ant-select.ant-select-focused .ant-select-selector {
    border-color: #dc2626 !important; /* red-600 */
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.1) !important;
}

/* Disabled states */
.ant-input:disabled,
.ant-input-number-disabled,
.ant-select-disabled .ant-select-selector {
    background-color: #f9fafb !important; /* gray-50 */
    border-color: #e5e7eb !important; /* gray-200 */
    color: #9ca3af !important; /* gray-400 */
    cursor: not-allowed !important;
}

/* Form labels */
.ant-form-item-label > label {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    color: #374151 !important; /* gray-700 */
    font-weight: 600 !important;
}

.ant-form-item-label > label.ant-form-item-required::before {
    color: #dc2626 !important; /* red-600 */
}

/* Button styling for Ant Design buttons */
.ant-btn {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.ant-btn-primary {
    background-color: #0f766e !important; /* teal-700 */
    border-color: #0f766e !important; /* teal-700 */
    color: #ffffff !important;
}

.ant-btn-primary:hover {
    background-color: #0d9488 !important; /* teal-600 */
    border-color: #0d9488 !important; /* teal-600 */
}

.ant-btn-primary:focus {
    background-color: #0d9488 !important; /* teal-600 */
    border-color: #0d9488 !important; /* teal-600 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

.ant-btn-default {
    background-color: #ffffff !important;
    border-color: #d1d5db !important; /* gray-300 */
    color: #374151 !important; /* gray-700 */
}

.ant-btn-default:hover {
    background-color: #f9fafb !important; /* gray-50 */
    border-color: #9ca3af !important; /* gray-400 */
    color: #374151 !important; /* gray-700 */
}

/* Validation message styling */
.validation-message {
    color: #dc2626 !important; /* red-600 */
    font-size: 0.875rem !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    margin-top: 0.25rem !important;
    display: block !important;
}

/* Form field spacing */
.ant-form-item {
    margin-bottom: 1.5rem !important;
}

.ant-form-item-label {
    padding-bottom: 0.5rem !important;
}

/* Width handled by Tailwind classes on components */

/* Loading state for buttons */
.ant-btn-loading {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
}

/* AntDesign Modal overrides */

/* Modal styling to match teal theme */
.ant-modal .ant-modal-header {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-bottom: 1px solid #e5e7eb !important; /* gray-200 */
}

.ant-modal .ant-modal-title {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    font-weight: 600 !important;
    color: #374151 !important; /* gray-700 */
}

.ant-modal .ant-modal-body {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    color: #6b7280 !important; /* gray-500 */
}

.ant-modal .ant-modal-footer {
    border-top: 1px solid #e5e7eb !important; /* gray-200 */
    padding: 1rem 1.5rem !important;
}

/* Confirm modal specific styling */
.ant-modal-confirm .ant-modal-confirm-title {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    font-weight: 600 !important;
    color: #374151 !important; /* gray-700 */
}

.ant-modal-confirm .ant-modal-confirm-content {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    color: #6b7280 !important; /* gray-500 */
    margin-top: 0.5rem !important;
}

.ant-modal-confirm .ant-modal-confirm-content p {
    margin-bottom: 0.5rem !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-modal-confirm .ant-modal-confirm-content strong {
    color: #374151 !important; /* gray-700 */
    font-weight: 600 !important;
}

/* Modal buttons styling - Override to use teal instead of red for consistency */
.ant-modal .ant-btn-primary.ant-btn-dangerous {
    background-color: #0f766e !important; /* teal-700 */
    border-color: #0f766e !important; /* teal-700 */
    color: #ffffff !important;
}

.ant-modal .ant-btn-primary.ant-btn-dangerous:hover {
    background-color: #0d9488 !important; /* teal-600 */
    border-color: #0d9488 !important; /* teal-600 */
}

.ant-modal .ant-btn-primary.ant-btn-dangerous:focus {
    background-color: #0d9488 !important; /* teal-600 */
    border-color: #0d9488 !important; /* teal-600 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important;
}

/* Enhanced Tailwind integration for Ant Design components */

/* Input components - apply focus states via CSS to avoid double borders */
.ant-input.w-full.border.border-gray-300:focus {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important; /* teal-100 ring */
    outline: none !important;
}

.ant-input.w-full.border.border-gray-300:hover {
    border-color: #0f766e !important; /* teal-700 */
}

/* AntDesign Collapse Component Teal Theme Overrides */

/* Collapse panel styling */
.ant-collapse {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    background-color: transparent !important;
    border: none !important;
}

.ant-collapse > .ant-collapse-item {
    border: 1px solid #e5e7eb !important; /* gray-200 */
    border-radius: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    background-color: #ffffff !important;
}

.ant-collapse > .ant-collapse-item:last-child {
    margin-bottom: 0 !important;
}

/* Collapse header styling */
.ant-collapse > .ant-collapse-item > .ant-collapse-header {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    padding: 1rem 1.5rem !important;
    background-color: #ffffff !important;
    border-radius: 0.5rem !important;
    color: #374151 !important; /* gray-700 */
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

    .ant-collapse > .ant-collapse-item > .ant-collapse-header:hover {
        background-color: #f3f4f6 !important; /* teal-50 */
        color: #0f766e !important; /* teal-700 */
    }

/* Collapse expand icon styling */
.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
    color: #6b7280 !important; /* gray-500 */
    font-size: 0.875rem !important;
    transition: color 0.2s ease !important;
}

.ant-collapse > .ant-collapse-item > .ant-collapse-header:hover .ant-collapse-arrow {
    color: #0f766e !important; /* teal-700 */
}

/* Active/expanded panel styling */
    .ant-collapse > .ant-collapse-item.ant-collapse-item-active > .ant-collapse-header {
        background-color: #f3f4f6 !important; /* teal-50 */
        color: #0f766e !important; /* teal-700 */
        border-bottom: 1px solid #e5e7eb !important; /* gray-200 */
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

.ant-collapse > .ant-collapse-item.ant-collapse-item-active > .ant-collapse-header .ant-collapse-arrow {
    color: #0f766e !important; /* teal-700 */
}

/* Collapse content styling */
.ant-collapse-content {
    background-color: #ffffff !important;
    border-top: none !important;
    border-bottom-left-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
}

.ant-collapse-content > .ant-collapse-content-box {
    padding: 0 !important;
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    background-color: #ffffff !important;
}

/* Enhanced visual separation for voyage details */
.ant-collapse > .ant-collapse-item.ant-collapse-item-active > .ant-collapse-content {
    background-color: #ffffff !important;
    border-top: 1px solid #e5e7eb !important; /* gray-200 */
}

/* Ensure white background for all voyage content areas */
.ant-collapse-content .voyage-details-content {
    background-color: #ffffff !important;
}

/* Improved spacing for voyage information sections */
.voyage-section-header {
    border-bottom: 2px solid #f0fdfa !important; /* teal-50 */
    padding-bottom: 0.75rem !important;
    margin-bottom: 1rem !important;
}

/* Enhanced visual hierarchy for voyage data */
.voyage-data-row {
    border-bottom: 1px solid #f9fafb !important; /* gray-50 */
    padding: 0.75rem 0 !important;
}

.voyage-data-row:last-child {
    border-bottom: none !important;
}

/* Ghost collapse styling (transparent background) */
.ant-collapse.ant-collapse-ghost {
    background-color: transparent !important;
}

.ant-collapse.ant-collapse-ghost > .ant-collapse-item {
    border: 1px solid #e5e7eb !important; /* gray-200 */
    background-color: #ffffff !important;
}

.ant-collapse.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content {
    background-color: #ffffff !important;
}

/* VoyageSection specific styling - prevent background color change on hover */
.ant-collapse > .ant-collapse-item:hover {
    background-color: #ffffff !important;
}

.ant-collapse > .ant-collapse-item > .ant-collapse-header:hover {
    background-color: #ffffff !important;
}

/* AntDesign Card Component Teal Theme Overrides */

/* Card styling */
.ant-card {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-radius: 0.5rem !important;
    border: 1px solid #e5e7eb !important; /* gray-200 */
    background-color: #ffffff !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    transition: all 0.2s ease !important;
}

.ant-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
}

/* Card header styling */
.ant-card-head {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    border-bottom: 1px solid #f3f4f6 !important; /* gray-100 */
    padding: 1rem 1.5rem !important;
    background-color: #ffffff !important;
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
}

.ant-card-head-title {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    font-weight: 600 !important;
    color: #374151 !important; /* gray-700 */
}

/* Card body styling */
.ant-card-body {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    padding: 1.5rem !important;
    color: #6b7280 !important; /* gray-500 */
}

/* Card actions styling */
.ant-card-actions {
    border-top: 1px solid #f3f4f6 !important; /* gray-100 */
    background-color: #f9fafb !important; /* gray-50 */
    border-bottom-left-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
}

.ant-card-actions > li {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
}

.ant-card-actions > li > span {
    color: #6b7280 !important; /* gray-500 */
    transition: color 0.2s ease !important;
}

.ant-card-actions > li > span:hover {
    color: #0f766e !important; /* teal-700 */
}

/* Card meta styling */
.ant-card-meta-title {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    font-weight: 600 !important;
    color: #374151 !important; /* gray-700 */
}

.ant-card-meta-description {
    font-family: 'Sofia Pro', ui-sans-serif, system-ui, sans-serif !important;
    color: #6b7280 !important; /* gray-500 */
}

/* InputNumber components - enhanced targeting for focus states */
.ant-input-number.w-full.border.border-gray-300:focus-within,
.ant-input-number.w-full.border.border-gray-300.ant-input-number-focused {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important; /* teal-100 ring */
}

.ant-input-number.w-full.border.border-gray-300:hover {
    border-color: #0f766e !important; /* teal-700 */
}

/* Force InputNumber focus with maximum specificity */
.ant-input-number.w-full.border.border-gray-300.rounded-md.bg-white:focus-within {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important; /* teal-100 ring */
}

/* InputNumber when input inside is focused */
.ant-input-number.w-full .ant-input-number-input:focus {
    border-color: transparent !important;
    box-shadow: none !important;
    outline: none !important;
}

.ant-input-number.w-full:has(.ant-input-number-input:focus) {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important; /* teal-100 ring */
}

/* Select components - apply focus states via CSS */
.ant-select.w-full.border.border-gray-300.ant-select-focused .ant-select-selector,
.ant-select.w-full.border.border-gray-300.ant-select-open .ant-select-selector {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important; /* teal-100 ring */
}

.ant-select.w-full.border.border-gray-300:hover .ant-select-selector {
    border-color: #0f766e !important; /* teal-700 */
}

/* Force full width for InputNumber with Tailwind classes */
.ant-input-number.w-full {
    width: 100% !important;
    display: block !important;
}

.ant-input-number.w-full .ant-input-number-input-wrap {
    width: 100% !important;
}

/* Alternative approach for InputNumber focus - target by class combination */
.ant-input-number[class*="w-full"][class*="border"][class*="border-gray-300"]:focus-within {
    border-color: #0f766e !important; /* teal-700 */
    box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.1) !important; /* teal-100 ring */
}

/* Focus ring for accessibility */
.ant-input:focus-visible,
.ant-input-number:focus-visible,
.ant-select:focus-visible .ant-select-selector {
    outline: 2px solid #0f766e !important;
    outline-offset: 2px !important;
}


.ant-input-number-handler:hover .ant-input-number-handler-up-inner, .ant-input-number-handler:hover .ant-input-number-handler-down-inner {
    color: #0f766e !important;
}

.ant-table-row-expand-icon:focus, .ant-table-row-expand-icon:hover, .ant-table-row-expand-icon:active {
    color: #0f766e !important;
}