using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;
using Klee.Domain.Services.Repositories.InvoiceManagement;
using Microsoft.EntityFrameworkCore;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.InvoiceManagement.Invoices
{
    public class GetOrganizationInvoicesQueryHandler
        : QueryHandlerAsync<GetOrganizationInvoicesQuery, IReadOnlyList<InvoiceListItem>>
    {
        #region PROPERTIES
        private IVoyageInvoiceSrpRepository VoyageInvoiceSrpRepository { get; }
        #endregion

        #region CONSTRUCTORS
        public GetOrganizationInvoicesQueryHandler(IVoyageInvoiceSrpRepository voyageInvoiceSrpRepository)
        {
            VoyageInvoiceSrpRepository = voyageInvoiceSrpRepository;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<InvoiceListItem>> ExecuteAsync(GetOrganizationInvoicesQuery query,
            CancellationToken cancellationToken = new CancellationToken())
        {
            string organizationId = query.OrganizationId;

            // Get InvoiceListItems from DB with joins
            List<InvoiceListItem> invoiceListItems =
                await VoyageInvoiceSrpRepository.Entities(query)
                    .Where(vi => vi.BookingOrganizationId == organizationId || vi.OperatorOrganizationId == organizationId)
                    .Include(vi => vi.Voyage)
                        .ThenInclude(v => v.Vehicle)
                    .Include(vi => vi.Voyage)
                        .ThenInclude(v => v.Operator)
                    .Include(vi => vi.BookingOrganization)
                    .Include(vi => vi.OperatorOrganization)
                    .OrderByDescending(vi => vi.Voyage.StartDateTime)
                    .Select(vi => new InvoiceListItem()
                    {
                        VoyageInvoiceId = vi.VoyageInvoiceId,
                        VoyageId = vi.VoyageId,
                        BookingOrganizationId = vi.BookingOrganizationId,
                        OperatorOrganizationId = vi.OperatorOrganizationId,
                        
                        // Voyage details
                        VesselName = vi.Voyage.Vehicle.VehicleName,
                        StartDateTime = vi.Voyage.StartDateTime,
                        EndDateTime = vi.Voyage.EndDateTime,
                        
                        // Operator details
                        OperatorId = vi.Voyage.OperatorId ?? "",
                        OperatorFirstName = vi.Voyage.Operator != null ? vi.Voyage.Operator.FirstName : "",
                        OperatorLastName = vi.Voyage.Operator != null ? vi.Voyage.Operator.LastName : "",
                        OperatorOrganizationName = vi.OperatorOrganization.Name,

                        // Booking organization
                        BookingOrganizationName = vi.BookingOrganization.Name,
                        
                        // Invoice details
                        TotalAmountInEuros = vi.TotalAmountInEuros,
                        Status = vi.Status,
                        PaymentDate = vi.PaymentDate,
                        CreatedDate = vi.CreatedDateTimeUtc
                    })
                    .ToListAsync(cancellationToken: cancellationToken);

            // Filter "IsActive" (if needed)
            if (query.IsActive != null)
            {
                invoiceListItems = invoiceListItems.Where(vi => vi.Status != VoyageInvoiceStatus.None).ToList();
            }

            return invoiceListItems;
        }
        #endregion
    }
}
