﻿using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Klee.Domain.Services.UserContextService;
using Paramore.Darker;
using System.Threading.Tasks;
using System.Threading;
using System;

namespace Klee.Domain.Services.QueryHandlers.OrganizationOperatorManagement.OrganizationOperators;

public sealed class GetOrganizationOperatorQueryHandler
    : QueryHandlerAsync<GetOrganizationOperatorQuery, Operator>
{
    #region PROPERTIES
    private IOperatorSrpRepository OperatorSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public GetOrganizationOperatorQueryHandler(IOperatorSrpRepository operatorSrpRepository, IUserContextHelperService userContextHelperService)
    {
        this.OperatorSrpRepository = operatorSrpRepository;
        this.UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<Operator> ExecuteAsync(GetOrganizationOperatorQuery query,
        CancellationToken cancellationToken = new CancellationToken())
    {
        // Init
        string operatorId = query.OperatorId;

        // Get Operator
        Operator operatorObj = await this.OperatorSrpRepository.FindAsync(_ => _.OperatorId == operatorId &&
                                                             _.EntityPartitionKey == operatorId, query);

        //Check if organization of logged in user is the same as Operator organization
        string userOrganizationId = await this.UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);
        if (userOrganizationId != operatorObj.OrganizationId)
        {
            throw new UnauthorizedAccessException("You do not have access to this operator.");
        }
        
        return operatorObj;
    }
    #endregion
}