﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	<LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Autofac" Version="8.2.0" />
	  <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.14" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Analyzers" Version="8.0.14" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.14" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.14" />
	  <PackageReference Include="Renoir.Application.EF.Core" Version="1.1.25078.1-beta" />
      <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.8" />
	  <PackageReference Include="Microsoft.Data.Sqlite.Core" Version="8.0.14" />
	  <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
	  <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />
	  <PackageReference Include="SQLitePCLRaw.bundle_e_sqlite3" Version="2.1.10" />
	  <PackageReference Include="SQLitePCLRaw.core" Version="2.1.10" />
	  <PackageReference Include="Z.EntityFramework.Plus.EFCore" Version="8.103.7.2" />
	  <PackageReference Include="Paramore.Brighter.Extensions.DependencyInjection" Version="9.9.4" />
    <PackageReference Include="Paramore.Darker.AspNetCore" Version="4.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Klee.Domain.Entities\Klee.Domain.Entities.csproj" />
    <ProjectReference Include="..\Klee.Domain.Messages\Klee.Domain.Messages.csproj" />
    <ProjectReference Include="..\Klee.Domain.Services\Klee.Domain.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Data\EntityTypeConfigurations\OperatorManagement\" />
    <Folder Include="Data\EntityTypeConfigurations\StationManagement\" />
    <Folder Include="Data\EntityTypeConfigurations\RocManagement\" />
    <Folder Include="Data\EntityTypeConfigurations\UserManagement\Users\" />
    <Folder Include="Repositories\UserManagement\" />
    <Folder Include="Repositories\StationManagement\" />
    <Folder Include="Repositories\RocManagement\" />
  </ItemGroup>

</Project>
