using System;
using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;
using Klee.Domain.Services;
using Renoir.Application.Messages.Commands.Common;
using Renoir.Application.Validations.Helpers;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorCreate;

public class AddOperatorViewModel
{
    #region FIELDS
    private readonly ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES
    [Required]
    public string FirstName { get; set; } = "";

    [Required]
    public string LastName { get; set; } = "";

    [Required]
    [EmailAddress]
    public string Email { get; set; } = "";

    public string Biography { get; set; } = "";
    public int YearsOfExperience { get; set; } = 0;
    public int YearsOfRemoteExperience { get; set; } = 0;

    [Range(0.00, 1000.00, ErrorMessage = "Hourly rate must be between €0.01 and €1000.00")]
    public double HourlyRateInEuros { get; set; } = 0.0;

    public IEnumerable<string> SelectedQualifications { get; set; } = new List<string>();

    // Working Days - Individual day selection
    public bool Monday { get; set; } = true;
    public bool Tuesday { get; set; } = true;
    public bool Wednesday { get; set; } = true;
    public bool Thursday { get; set; } = true;
    public bool Friday { get; set; } = true;
    public bool Saturday { get; set; } = false;
    public bool Sunday { get; set; } = false;

    // Working Hours - Start and End times
    public DateTime RegularStartTime { get; set; } = DateTime.Today.AddHours(9);
    public DateTime RegularEndTime { get; set; } = DateTime.Today.AddHours(17);

    #endregion

    #region CONSTRUCTORS
    public AddOperatorViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS

    public async Task CreateOperatorAsync()
    {
        try
        {
            // Create the command
            CreateOrganizationOperatorCommand createCommand = await NewCreateOrganizationOperatorCommandAsync();

            // Execute the command
            await this._srpProcessors.CommandProcessor.SendAsync(createCommand);
        }
        catch
        {
            throw;
        }
    }

    public async Task<CreateOrganizationOperatorCommand> NewCreateOrganizationOperatorCommandAsync()
    {
        ICommandContext commandContext = await _srpProcessors.GetCommandContextAsync();
            
        return new CreateOrganizationOperatorCommand(commandContext)
        {
            FirstName = FirstName,
            LastName = LastName,
            Email = Email,
            HourlyRateInEuros = HourlyRateInEuros,
            Qualifications = SelectedQualifications.Select(Enum.Parse<QualificationTypeIds>).ToList(),
            YearsOfExperience = YearsOfExperience,
            YearsOfRemoteExperience = YearsOfRemoteExperience,
            WeekDays = GetWorkingDaysFromCheckboxes(),
            RegularStartTime = RegularStartTime,
            RegularEndTime = RegularEndTime,
            Biography = Biography
        };
    }

    private WeekDaysIds GetWorkingDaysFromCheckboxes()
    {
        WeekDaysIds weekDays = WeekDaysIds.None;

        if (Monday) weekDays |= WeekDaysIds.Monday;
        if (Tuesday) weekDays |= WeekDaysIds.Tuesday;
        if (Wednesday) weekDays |= WeekDaysIds.Wednesday;
        if (Thursday) weekDays |= WeekDaysIds.Thursday;
        if (Friday) weekDays |= WeekDaysIds.Friday;
        if (Saturday) weekDays |= WeekDaysIds.Saturday;
        if (Sunday) weekDays |= WeekDaysIds.Sunday;

        return weekDays;
    }

    #endregion

    #region METHODS - VALIDATE
    public async Task<IEnumerable<ValidationResult>> Validate()
    {
        // Init
        List<ValidationResult> validationResults = new List<ValidationResult>();

        // Validate Command
        CreateOrganizationOperatorCommandValidator createOrganizationOperatorCommandValidator = new CreateOrganizationOperatorCommandValidator();
        (await createOrganizationOperatorCommandValidator.ValidateAsync(await this.NewCreateOrganizationOperatorCommandAsync()))
            .AddTo(validationResults);

        return validationResults;
    }
    #endregion
}
