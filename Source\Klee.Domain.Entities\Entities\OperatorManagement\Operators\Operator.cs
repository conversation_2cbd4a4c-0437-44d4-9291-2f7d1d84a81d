﻿using Renoir.Application.Domain;
using Renoir.SoftwareEnvironments;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Klee.Domain.Entities.OperatorManagement.Operators.Data;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.QualificationManagement.Qualifications;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Monet.Helpers;

namespace Klee.Domain.Entities.OperatorManagement.Operators;

public class Operator
    : DomainEntityAggregateRootBase<long>
{
    #region FIELDS

    private string _displayName = "";

    #endregion

    #region PROPERTIES - IDENTIFICATION
    /// <summary>
    /// The Seafar internal id of the operator
    /// </summary>
    [Required]
    public string OperatorId { get; internal set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 
    /// </summary>
    public string FirstName { get; internal set; } = "";

    /// <summary>
    /// 
    /// </summary>
    public string LastName { get; internal set; } = "";

    public string DisplayName {
        get => this.GetDisplayName();
        internal set => this._displayName = value;
    }

    /// <summary>
    /// 
    /// </summary>
    public string OperatorEmail { get; internal set; } = "";

    /// <summary>
    /// 
    /// </summary>
    public double HourlyRateInEuros { get; internal set; } = 0.0;

    /// <summary>
    /// Set of qualifications of the operator (patents, certificates, ...)
    /// </summary>
    public List<QualificationTypeIds> Qualifications { get; internal set; } = new();

    /// <summary>
    /// Total years of professional experience
    /// </summary>
    public int YearsOfExperience { get; internal set; } = 0;

    /// <summary>
    /// Years of remote operations experience
    /// </summary>
    public int YearsOfRemoteExperience { get; internal set; } = 0;

    /// <summary>
    /// Days of the week the operator is available to work
    /// </summary>
    public WeekDaysIds WorkingDays { get; internal set; } = WeekDaysIds.Monday | WeekDaysIds.Tuesday | WeekDaysIds.Wednesday | WeekDaysIds.Thursday | WeekDaysIds.Friday;

    /// <summary>
    /// Operator's typical start time
    /// </summary>
    public DateTime RegularStartTime { get; internal set; } = DateTime.Today.AddHours(9); // 9:00 AM

    /// <summary>
    /// Operator's typical end time
    /// </summary>
    public DateTime RegularEndTime { get; internal set; } = DateTime.Today.AddHours(17); // 5:00 PM

    /// <summary>
    /// Free-text description of operator's background and experience
    /// </summary>
    public string Biography { get; internal set; } = "";
    #endregion

    #region PROPERTIES - SYSTEM
    /// <summary>
    /// The software environment on which the Operator is used
    /// </summary>
    public SoftwareEnvironmentIds SoftwareEnvironmentId { get; internal set; } = SoftwareEnvironmentIds.Prod;

    /// <summary>
    /// Is active when the Operator is still in active use
    /// </summary>
    public bool? IsActive { get; internal set; } = true;

    #endregion

    #region PROPERTIES - RELATIONS 
    /// <summary>
    /// ID of the organization to which the operator belongs
    /// </summary>
    [Required]
    public string OrganizationId { get; internal set; } = "";

    public Organization Organization { get; internal set; }
    #endregion

    #region CONSTRUCTORS

    public Operator()
{
}
public Operator(string operatorId, Organization organization) {
        this.OperatorId = operatorId;
        Organization = organization;
    }
    #endregion

    #region METHODS - ENTITY
    public override string CreateEntityPartitionKey()
    {
        return this.OperatorId;
    }

    public override string GetEntityId2()
    {
        return this.OperatorId;
    }

    public override string GetEntityTypeName()
    {
        return "Operator";
    }

    private string GetDisplayName() {
        if (this._displayName.IsNotNullOrWhiteSpace()) {
            return this._displayName;
        }
        else {
            // Init
            string displayName = this.FirstName + " " + this.LastName;

            //
            return displayName.IsNotNullOrWhiteSpace() ? displayName : "Undefined";
        }
    }
    #endregion
}