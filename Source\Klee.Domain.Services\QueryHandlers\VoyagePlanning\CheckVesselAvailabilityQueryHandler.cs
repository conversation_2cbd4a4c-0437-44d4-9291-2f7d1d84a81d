using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Messages.Queries.VoyagePlanning;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Klee.Domain.Services.UserContextService;
using Microsoft.EntityFrameworkCore;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.VoyagePlanning;

public sealed class CheckVesselAvailabilityQueryHandler
    : QueryHandlerAsync<CheckVesselAvailabilityQuery, VesselAvailabilityResult>
{
    #region PROPERTIES
    private IVoyageSrpRepository VoyageSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
    #endregion

    #region CONSTRUCTORS
    public CheckVesselAvailabilityQueryHandler(IVoyageSrpRepository voyageSrpRepository, IUserContextHelperService userContextHelperService)
    {
        this.VoyageSrpRepository = voyageSrpRepository;
        this.UserContextHelperService = userContextHelperService;
    }
    #endregion

    #region METHODS
    public override async Task<VesselAvailabilityResult> ExecuteAsync(CheckVesselAvailabilityQuery query,
                                                                      CancellationToken cancellationToken = new CancellationToken())
    {
        // Get current user's organization
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(query.Context.User);

        // Check for conflicting voyages with exact time matching (no buffer for vessels)
        var conflictingVoyage = await VoyageSrpRepository.Entities(query)
            .Include(v => v.BookingOrganization)
            .Where(v => v.IsActive == true)
            .Where(v => v.VehicleId == query.VesselId)
            .Where(v => v.EndDateTime > query.VoyageStartDateTime && v.StartDateTime < query.VoyageEndDateTime)
            .Select(v => new ConflictingVoyageInfo
            {
                VoyageId = v.VoyageId,
                StartDateTime = v.StartDateTime,
                EndDateTime = v.EndDateTime,
                Description = v.Description,
                BookingOrganizationName = v.BookingOrganization.Name
            })
            .FirstOrDefaultAsync(cancellationToken);

        return new VesselAvailabilityResult
        {
            IsAvailable = conflictingVoyage == null,
            ConflictingVoyage = conflictingVoyage
        };
    }
    #endregion
}
