using System.ComponentModel.DataAnnotations;
using EnumsNET;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles.Data;
using Klee.Domain.Messages.Queries.VoyagePlanning;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Services;
using Renoir.Application.Messages.Queries.Common;

namespace Klee.Web.App.Components.Pages.VoyagePlanning;

public class PlanVoyageViewModel
{
    #region FIELDS
    private readonly ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES
    [Required(ErrorMessage = "Please select a vessel")]
    public string SelectedVesselId { get; set; } = "";

    [Required(ErrorMessage = "Please select a start date and time")]
    public DateTime StartDateTime { get; set; } = DateTime.Now;

    [Required(ErrorMessage = "Please select an end date and time")]
    public DateTime EndDateTime { get; set; } = DateTime.Now.AddHours(1);

    public IEnumerable<QualificationTypeIds> SelectedQualifications { get; set; } = new List<QualificationTypeIds>();

    public string Description { get; set; } = "";

    // Data for dropdowns
    public List<OrganizationVehicleListItem> OrganizationVessels { get; set; } = new();
    #endregion

    #region CONSTRUCTORS
    public PlanVoyageViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS - DATA LOADING
    public async Task LoadOrganizationVesselsAsync()
    {
        IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
        IReadOnlyList<OrganizationVehicleListItem>? vessels = await _srpProcessors.QueryProcessor.ExecuteAsync(new GetOrganizationVehicleListQuery(queryContext));
        OrganizationVessels = vessels.ToList();
    }

    #endregion

    #region METHODS - VALIDATION
    public async Task<IEnumerable<ValidationResult>> Validate()
    {
        var validationResults = new List<ValidationResult>();

        // Basic property validation
        ValidationContext validationContext = new ValidationContext(this);
        Validator.TryValidateObject(this, validationContext, validationResults, true);

        // Custom validation
        // Ensure that start date is not in the past and end date is after start date
        if (StartDateTime < DateTime.Now)
        {
            validationResults.Add(new ValidationResult("Start date cannot be in the past", new[] { nameof(StartDateTime) }));
        }
        if (StartDateTime >= EndDateTime)
        {
            validationResults.Add(new ValidationResult("End date must be after start date", new[] { nameof(EndDateTime) }));
        }
        
        // Vessel availability validation
        if (!string.IsNullOrEmpty(SelectedVesselId))
        {
            try
            {
                IQueryContext queryContext = await _srpProcessors.GetQueryContextAsync();
                CheckVesselAvailabilityQuery vesselAvailabilityQuery = new CheckVesselAvailabilityQuery(queryContext)
                {
                    VesselId = SelectedVesselId,
                    VoyageStartDateTime = StartDateTime,
                    VoyageEndDateTime = EndDateTime
                };

                VesselAvailabilityResult availabilityResult = await _srpProcessors.QueryProcessor.ExecuteAsync(vesselAvailabilityQuery);

                if (!availabilityResult.IsAvailable && availabilityResult.ConflictingVoyage != null)
                {
                    string conflictMessage = $"The selected vessel is not available for the requested time slot. " +
                                           $"It is already booked for another voyage from {availabilityResult.ConflictingVoyage.StartDateTime:yyyy-MM-dd HH:mm} " +
                                           $"to {availabilityResult.ConflictingVoyage.EndDateTime:yyyy-MM-dd HH:mm} " +
                                           $"by {availabilityResult.ConflictingVoyage.BookingOrganizationName}.";

                    validationResults.Add(new ValidationResult(conflictMessage, new[] { nameof(SelectedVesselId) }));
                }
            }
            catch (Exception)
            {
                // If vessel availability check fails, add a generic error
                validationResults.Add(new ValidationResult("Unable to verify vessel availability. Please try again.", new[] { nameof(SelectedVesselId) }));
            }
        }

        return validationResults;
    }
    #endregion
}
