using System.ComponentModel.DataAnnotations;
using Klee.Domain.Services;
using Klee.Web.App.Services.Authentication;
using Microsoft.AspNetCore.Components;
using Renoir.Web.Razor.Services.UserNotifications;
using Renoir.Web.Razor.Services.UserNotifications.Helpers;
using AntDesign;
using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorList;

namespace Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorCreate;

public class AddOperatorViewBase : ComponentBase
{
    #region DI
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private INotificationService NotificationService { get; set; }

    [Inject]
    private ILogger<AddOperatorViewBase> Logger { get; set; }

    [Inject]
    private ISrpProcessors SrpProcessors { get; set; }

    [Inject]
    private IUserAuthenticationService UserAuthenticationService { get; set; }
    #endregion

    #region PROPERTIES
    protected AddOperatorViewModel ViewModel { get; set; }
    protected List<string> ValidationErrors { get; set; } = new();
    protected bool IsLoading { get; set; } = true;
    protected bool IsSubmitting { get; set; } = false;
    #endregion

    #region METHODS - OVERRIDE
    protected override async Task OnInitializedAsync()
    {
     
        // Check user authorization
        bool isUserOrganizationAdmin = await this.UserAuthenticationService.IsCurrentUserOrganizationAdminAsync();
        
        if (!isUserOrganizationAdmin)
        {
            // Redirect to operators list if not admin
            NavigationManager.NavigateTo(Operators.GetUri());
            return;
        }

        // Initialize ViewModel
        this.ViewModel = new AddOperatorViewModel(this.SrpProcessors);
        this.IsLoading = false;
        await base.OnInitializedAsync();
    }
    #endregion

    #region EVENT HANDLERS
    protected void NavigateToOperatorList()
    {
        this.NavigationManager.NavigateTo(Operators.GetUri());
    }

    protected async Task HandleSubmit()
    {
        if (this.IsSubmitting)
            return;

        try
        {
            this.IsSubmitting = true;
            StateHasChanged();

            ValidationErrors.Clear();
            List<ValidationResult> validationResult = (await this.ViewModel.Validate()).ToList();

            if (validationResult.IsEmpty())
            {
                // Create Operator
                await this.ViewModel.CreateOperatorAsync();

                //Notify - don't await this call to not block the UI
                this.NotificationService.Success(new NotificationConfig()
                {
                    Message = "Created",
                    Description = $"Operator '{ViewModel.FirstName} {ViewModel.LastName}' has been successfully created.",
                    Duration = 4.5
                });

                NavigateToOperatorList();
            }
            else
            {
                foreach (ValidationResult result in validationResult) {
                    if (result.ErrorMessage != null) {
                        ValidationErrors.Add(result.ErrorMessage);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            #region Logging
            this.Logger.LogError(exception, "Exception when creating operator.");
            #endregion

            // Show error notification
            this.NotificationService.Error(new NotificationConfig()
            {
                Message = "Error",
                Description = "Failed to create operator. Please check your input and try again.",
                Duration = 6.0
            });

            ValidationErrors.Add($"An error occurred while creating the operator: {exception.Message}");
        }
        finally
        {
            this.IsSubmitting = false;
            StateHasChanged();
        }
    }
    #endregion
}
