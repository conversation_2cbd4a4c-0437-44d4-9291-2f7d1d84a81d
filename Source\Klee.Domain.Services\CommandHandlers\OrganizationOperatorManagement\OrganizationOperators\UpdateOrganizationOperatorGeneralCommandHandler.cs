﻿using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;

namespace Klee.Domain.Services.CommandHandlers.OrganizationOperatorManagement.OrganizationOperators;

public sealed class UpdateOrganizationOperatorGeneralCommandHandler
    : RequestHandlerAsync<UpdateOrganizationOperatorGeneralCommand> {
    #region PROPERTIES

    private IOperatorSrpRepository OperatorSrpRepository { get; }

    #endregion

    #region CONSTRUCTORS

    public UpdateOrganizationOperatorGeneralCommandHandler(IOperatorSrpRepository operatorSrpRepository) {
        this.OperatorSrpRepository = operatorSrpRepository;
    }

    #endregion

    #region METHODS

    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(UpdateOrganizationOperatorGeneralCommandValidator))]
    public override async Task<UpdateOrganizationOperatorGeneralCommand> HandleAsync(UpdateOrganizationOperatorGeneralCommand command,
        CancellationToken cancellationToken = new CancellationToken()) {

        //Get Operator (if it exists) 
        if (await OperatorSrpRepository.ExistsAsync(_ => _.OperatorId == command.OperatorId &&
                                                    _.EntityPartitionKey == command.OperatorId,
                command)) {

            Operator operatorObj = await OperatorSrpRepository.FindAsync(_ => _.OperatorId == command.OperatorId &&
                                                            _.EntityPartitionKey == command.OperatorId,
                command);

            operatorObj.FirstName = command.FirstName;
            operatorObj.LastName = command.LastName;
            operatorObj.OperatorEmail = command.Email;
            operatorObj.HourlyRateInEuros = command.HourlyRateInEuros;
            operatorObj.YearsOfExperience = command.YearsOfExperience;
            operatorObj.YearsOfRemoteExperience = command.YearsOfRemoteExperience;
            operatorObj.WorkingDays = command.WeekDays;
            operatorObj.RegularStartTime = command.RegularStartTime;
            operatorObj.RegularEndTime = command.RegularEndTime;
            operatorObj.Biography = command.Biography;

            // Update
            await OperatorSrpRepository.UpdateAsync(operatorObj, command);

            // Set Result
            command.Result.EntityId = operatorObj.EntityId;

        }
        else {
            throw new EntityNotFoundException(
                $"Operator with operator id '{command.OperatorId}' not found.");
        }

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}