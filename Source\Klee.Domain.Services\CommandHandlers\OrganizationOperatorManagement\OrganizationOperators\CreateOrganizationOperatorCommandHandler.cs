﻿using Klee.Domain.Entities.Common.Exceptions;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators.Validators;
using Klee.Domain.Messages.Commands.OrganizationOperatorManagement.OrganizationOperators;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Klee.Domain.Services.UserContextService;
using Paramore.Brighter;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestValidation;
using System.Threading.Tasks;
using System.Threading;

namespace Klee.Domain.Services.CommandHandlers.OrganizationOperatorManagement.OrganizationOperators;

public sealed class CreateOrganizationOperatorCommandHandler
    : RequestHandlerAsync<CreateOrganizationOperatorCommand>
{
    #region PROPERTIES
    private IOperatorSrpRepository OperatorSrpRepository { get; }
    private IUserContextHelperService UserContextHelperService { get; }
#endregion

#region CONSTRUCTORS
public CreateOrganizationOperatorCommandHandler(IOperatorSrpRepository operatorSrpRepository, IUserContextHelperService userContextHelperService)
    {
        this.OperatorSrpRepository = operatorSrpRepository;
        this.UserContextHelperService = userContextHelperService;
}
    #endregion

    #region METHODS
    [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
    [RequestValidationCommandHandler(step: 2, HandlerTiming.Before, typeof(CreateOrganizationOperatorCommandValidator))]
    public override async Task<CreateOrganizationOperatorCommand> HandleAsync(CreateOrganizationOperatorCommand command,
        CancellationToken cancellationToken = new ())
    {
       
        // Get Organization ID from logged in user
        string organizationId = await UserContextHelperService.GetUserOrganizationIdByClaimsAsync(command.Context.User);

        // Create
        Operator operatorObj = new Operator()
        {
            FirstName = command.FirstName,
            LastName = command.LastName,
            OperatorEmail = command.Email,
            HourlyRateInEuros = command.HourlyRateInEuros,
            OrganizationId = organizationId,
            Qualifications = command.Qualifications,
            YearsOfExperience = command.YearsOfExperience,
            YearsOfRemoteExperience = command.YearsOfRemoteExperience,
            WorkingDays = command.WeekDays,
            RegularStartTime = command.RegularStartTime,
            RegularEndTime = command.RegularEndTime,
            Biography = command.Biography,
        };

        // Save 
        await OperatorSrpRepository.AddAsync(operatorObj, command);

        // Set Result
        command.Result.EntityId = operatorObj.EntityId;

        return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
    }

    #endregion
}